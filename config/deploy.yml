# Name of your application. Used to uniquely configure containers.
service: flamint

# Name of the container image.
image: flamint/flamint

# Deploy to these servers.
servers:
  web:
    # Replace with your actual host machine IP address
    - flamint.me # e.g., 123.456.789.012
  # job:
  #   hosts:
  #     - ***********
  #   cmd: bin/jobs

# Enable SSL auto certification via Let's Encrypt and allow for multiple apps on a single web server.
# Note: If using Cloudflare, set encryption mode in SSL/TLS setting to "Full" to enable CF-to-app encryption.
proxy:
  ssl: true
  hosts:
    - flamint.me

# Credentials for your image host.
registry:
  # Specify the registry server, if you're not using Docker Hub
  # server: registry.digitalocean.com / ghcr.io / ...
  username: flamint

  # Always use an access token rather than real password when possible.
  password:
    - KAMAL_REGISTRY_PASSWORD

# Inject ENV variables into containers (secrets come from .kamal/secrets).
env:
  secret:
    - RAILS_MASTER_KEY
    - POSTGRES_DB
    - POSTGRES_USER
    - POSTGRES_PASSWORD
    - CLIENT_ID_CTRADER_APP
    - CLIENT_SECRET_CTRADER_APP
    - SELF_DOMAIN
    - INTERNAL_CONNECTION_STRING
    - INTERNAL_API_AUTH_KEY
    - MT5_URL_DEDAULT
    - CTRADER_TEST_ACCOUNT_ID
    - CTRADER_TEST_ACCOUNT_NUMBER
    - CTRADER_TEST_ACCESS_TOKEN
    - CTRADER_TEST_REFRESH_TOKEN
    - CTRADER_TEST_ACCESS_TOKEN_UPDATED_AT
    - MT5_TEST1_LOGIN
    - MT5_TEST1_PASSWORD
    - MT5_TEST1_SERVER
    - API_BASE_URL
  clear:
    # Run the Solid Queue Supervisor inside the web server's Puma process to do jobs.
    # When you start using multiple servers, you should split out job processing to a dedicated machine.
    SOLID_QUEUE_IN_PUMA: true

    # Set number of processes dedicated to Solid Queue (default: 1)
    JOB_CONCURRENCY: 3

    # Set number of cores available to the application on each server (default: 1).
    WEB_CONCURRENCY: 2

    # Match this to any external database server to configure Active Record correctly
    # Use flamint-db for a db accessory server on same machine via local kamal docker network.
    DB_HOST: flamint-db
    DB_PORT: 5433
    # Log everything from Rails
    RAILS_LOG_LEVEL: debug
    PYTHON_SCRIPT_MANAGER_URL: http://script_manager:8000
    VOLUME_SCRIPT_PATH: scripts
    VOLUME_MT5_CONFIG_PATH: mt5config
    CELERY_BROKER_URL: amqp://rabbitmq
    TZ: Asia/Ho_Chi_Minh

# Aliases are triggered with "bin/kamal <alias>". You can overwrite arguments on invocation:
# "bin/kamal logs -r job" will tail logs from the first server in the job section.
aliases:
  console: app exec --interactive --reuse "bin/rails console"
  shell: app exec --interactive --reuse "bash"
  logs: app logs -f
  dbc: app exec --interactive --reuse "bin/rails dbconsole"


# Use a persistent storage volume for sqlite database files and local Active Storage files.
# Recommended to change this to a mounted volume path that is backed up off server.
volumes:
  - "flamint_storage:/rails/storage"
  - /var/run/docker.sock:/var/run/docker.sock
  - scripts:/rails/external_scripts
  - mt5config:/rails/mt5config

# Bridge fingerprinted assets, like JS and CSS, between versions to avoid
# hitting 404 on in-flight requests. Combines all files from new and old
# version inside the asset_path.
asset_path: /rails/public/assets

# Configure the image builder.
builder:
  arch: amd64

  # # Build image via remote server (useful for faster amd64 builds on arm64 computers)
  # remote: ssh://docker@docker-builder-server
  #
  # # Pass arguments and secrets to the Docker build process
  # args:
  #   RUBY_VERSION: 3.4.2
  # secrets:
  #   - GITHUB_TOKEN
  #   - RAILS_MASTER_KEY

# Use a different ssh user than root
ssh:
  user: minhdc
  port: 2211
  keys_only: false

# Use accessory services (secrets come from .kamal/secrets).
accessories:
  db:
    image: postgres:latest
    # Use the same host IP as your web server
    host: flamint.me  # e.g., 123.456.789.012
    # Use a different port to avoid conflicts with existing databases
    port: "0.0.0.0:5433:5432"
    env:
      secret:
        - POSTGRES_USER
        - POSTGRES_DB
        - POSTGRES_PASSWORD
    # files:
    #   - config/postgres/production.conf:/etc/postgresql/postgresql.conf
    #   - db/production.sql:/docker-entrypoint-initdb.d/setup.sql
    directories:
      - data:/var/lib/postgresql/data
#   redis:
#     image: redis:7.0
#     host: ***********
#     port: 6379
#     directories:
#       - data:/data

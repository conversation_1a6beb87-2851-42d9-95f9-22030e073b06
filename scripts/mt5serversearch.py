#!/usr/bin/env python3
import pyautogui
import time
import sys

# Get command-line arguments
args = sys.argv[1:]

# Print the arguments
if __name__ == '__main__':
    pyautogui.press('esc')
    pyautogui.press('esc')
    pyautogui.hotkey('alt', 'f', 'a')
    for arg in args:
        for _ in range(3):
            pyautogui.press('tab')
        pyautogui.write(arg)
        pyautogui.press('enter')
        time.sleep(5)
    pyautogui.press('esc')

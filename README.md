# README

Command to run mt5 server search to add IP server to MT5:
docker exec -u 911:911 mt5 wine cmd /c "python /scripts/mt5serversearch.py <server-name>"

This README would normally document whatever steps are necessary to get the
application up and running.

Things you may want to cover:

* Ruby version

* System dependencies

* Configuration

* Database creation

* Database initialization

* How to run the test suite

* Services (job queues, cache servers, search engines, etc.)

* Deployment instructions

* ...

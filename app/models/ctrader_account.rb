class CtraderAccount < ApplicationRecord
  include Accountable

  require 'net/http'
  require 'uri'
  require 'json'

  validates :account_number, presence: true, uniqueness: true

  # Check if the access token is expired
  def token_expired?
    return true if access_token.blank? || refresh_token.blank?

    # If we don't have an updated_at timestamp or expires_in value, consider the token expired
    return true if updated_at.blank? || expires_in.blank?

    # Calculate the expiration time
    expiration_time = updated_at + expires_in.seconds

    # Check if the current time is past the expiration time
    Time.current > expiration_time
  end

  # Refresh the access token using the refresh token
  def refresh_access_token
    return false if refresh_token.blank?

    # Use the cTrader token endpoint
    token_url = "https://openapi.ctrader.com/apps/token"

    # Set up the request parameters
    params = {
      grant_type: 'refresh_token',
      refresh_token: refresh_token,
      client_id: ENV['CLIENT_ID_CTRADER_APP'],
      client_secret: ENV['CLIENT_SECRET_CTRADER_APP']
    }

    # Make the POST request to refresh the token
    begin
      uri = URI.parse(token_url)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true

      request = Net::HTTP::Post.new(uri.path, 'Content-Type' => 'application/x-www-form-urlencoded')
      request.set_form_data(params)
      response = http.request(request)

      if response.code == '200'
        # Parse the JSON response
        token_data = JSON.parse(response.body)

        # Update the account with the new token data
        update(
          access_token: token_data['access_token'],
          refresh_token: token_data['refresh_token'],
          expires_in: token_data['expires_in']
        )

        return true
      else
        Rails.logger.error("Failed to refresh token: #{response.body}")
        return false
      end
    rescue => e
      Rails.logger.error("Error refreshing token: #{e.message}")
      return false
    end
  end

  # Get a valid access token, refreshing if necessary
  def valid_access_token
    refresh_access_token if token_expired?
    access_token
  end
end

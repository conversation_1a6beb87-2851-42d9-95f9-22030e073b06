class ScriptManagerService
  include HTTParty
  base_uri ENV['PYTHON_SCRIPT_MANAGER_URL']

  def self.start_script(script_id)
    # Send as query parameter for FastAPI
    post('/scripts/start', query: { script_id: script_id })
  end

  def self.stop_script(script_id)
    # Send as query parameter for FastAPI
    post('/scripts/stop', query: { script_id: script_id })
  end

  def self.script_status(script_id)
    get('/scripts/status', query: { script_id: script_id })
  end

  def self.script_logs(script_id, lines = 100)
    # Get the logs for a script
    get('/scripts/logs', query: { script_id: script_id, lines: lines })
  end

  def self.system_status
    # Get the system status
    get('/system/status')
  end
end

class Api::InternalController < ActionController::API
  before_action :authenticate_internal_request

  # POST /api/internal/container/mt5_server_search
  def mt5_server_search
    server_name = params[:server_name]

    if server_name.blank?
      render json: { error: 'Server name is required' }, status: :bad_request
      return
    end

    # Execute the Docker command
    begin
      container_name = params[:container_name] || 'mt5'
      command = "docker exec -u 911:911 #{container_name} wine cmd /c \"python /scripts/mt5serversearch.py #{server_name}\""

      # Log the command being executed
      Rails.logger.info("Executing MT5 server search command: #{command}")

      # Execute the command and capture output
      output = `#{command} 2>&1`
      exit_status = $?.exitstatus

      if exit_status == 0
        render json: {
          success: true,
          container: container_name,
          server_name: server_name,
          output: output.strip
        }
      else
        render json: {
          success: false,
          container: container_name,
          server_name: server_name,
          error: output.strip,
          exit_status: exit_status
        }, status: :internal_server_error
      end
    rescue => e
      Rails.logger.error("Error executing MT5 server search: #{e.message}")
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  def mt5_escape_window
    # Execute the Docker command
    begin
      container_name = params[:container_name] || 'mt5'
      command = "docker exec -u 911:911 #{container_name} wine cmd /c \"python /scripts/sendescapetomt5.py\""

      # Log the command being executed
      Rails.logger.info("Executing MT5 escape window: #{command}")

      # Execute the command and capture output
      output = `#{command} 2>&1`
      exit_status = $?.exitstatus

      if exit_status == 0
        render json: {
          success: true,
          container: container_name,
          output: output.strip
        }
      else
        render json: {
          success: false,
          container: container_name,
          error: output.strip,
          exit_status: exit_status
        }, status: :internal_server_error
      end
    rescue => e
      Rails.logger.error("Error executing MT5 escape window: #{e.message}")
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  # POST /api/internal/container/script_notification
  def script_notification
    script_id = params[:script_id]
    status = params[:status]
    message = params[:message]

    if script_id.blank? || status.blank?
      render json: { error: 'Script ID and status are required' }, status: :bad_request
      return
    end

    # Find the script
    script = Script.find_by(id: script_id)

    unless script
      render json: { error: 'Script not found' }, status: :not_found
      return
    end

    # Log the notification
    Rails.logger.info("Script notification received: Script ID: #{script_id}, Status: #{status}, Message: #{message}")

    # Update script status in database if needed
    # This would depend on your application's requirements

    # You could also notify users via ActionCable or other means

    render json: {
      success: true,
      script_id: script_id,
      status: status,
      message: "Notification received for script #{script_id}"
    }
  end

  # POST /api/internal/bot_restart
  def bot_restart
    script_id = params[:script_id]

    if script_id.blank?
      render json: { error: 'Script ID is required' }, status: :bad_request
      return
    end

    # Find the script
    script = Script.find_by(id: script_id)

    unless script
      render json: { error: 'Script not found' }, status: :not_found
      return
    end

    begin
      # Queue the job to restart the bot
      BotActionJob.perform_later('restart', script.id, script.user_id)

      # Log the restart request
      Rails.logger.info("Bot restart request received: Script ID: #{script_id}")

      render json: {
        success: true,
        script_id: script_id,
        message: "Bot restart request queued successfully"
      }
    rescue => e
      Rails.logger.error("Error queuing bot restart: #{e.message}")
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  private

  def authenticate_internal_request
    # First check for the authentication key
    auth_key = params[:auth_key] || request.headers['X-Internal-Auth-Key']

    if auth_key.present?
      # Get the expected auth key from environment variable
      expected_auth_key = ENV['INTERNAL_API_AUTH_KEY']

      if expected_auth_key.present? && ActiveSupport::SecurityUtils.secure_compare(auth_key, expected_auth_key)
        return true
      else
        Rails.logger.warn("Invalid authentication key provided")
      end
    else
      Rails.logger.warn("No authentication key provided")
    end

    # If auth key check fails, fall back to IP-based check
    client_ip = request.remote_ip

    # Allow localhost for development
    return true if client_ip == '127.0.0.1' || client_ip == '::1'

    # Check if the request is coming from the Docker network
    # The Docker bridge network typically uses **********/16 or similar
    # The Kamal network might use a different range
    allowed_networks = [
      IPAddr.new('**********/12'),  # Common Docker network range
      IPAddr.new('***********/16')  # Common private network range
    ]

    is_allowed = allowed_networks.any? { |network| network.include?(client_ip) }

    unless is_allowed
      Rails.logger.warn("Unauthorized internal API access attempt from IP: #{client_ip}")
      render json: { error: 'Unauthorized' }, status: :unauthorized
      return false
    end

    true
  end
end

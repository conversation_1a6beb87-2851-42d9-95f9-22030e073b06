<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white">
        <% if @account_type == 'mt5' %>
          Add MT5 Account
        <% else %>
          Add CTrader Account
        <% end %>
      </h1>
    </div>

    <%= form_with(model: @account, url: accounts_path, class: "space-y-4") do |form| %>
      <% if @account.errors.any? %>
        <div class="bg-red-800 text-white p-4 rounded-lg mb-4">
          <h2 class="text-lg font-semibold mb-2">
            <%= pluralize(@account.errors.count, "error") %> prohibited this account from being saved:
          </h2>
          <ul class="list-disc list-inside">
            <% @account.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div>
        <%= form.label :name, "Account Name (optional)", class: "block text-sm font-medium text-gray-300 mb-1" %>
        <%= form.text_field :name, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", placeholder: "My Trading Account" %>
      </div>

      <%= form.hidden_field :accountable_type, value: @account_type == 'mt5' ? 'Mt5Account' : 'CtraderAccount' %>

      <% if @account_type == 'mt5' %>
        <div>
          <%= form.label :login, "Login", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.number_field :login, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true %>
        </div>

        <div>
          <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.password_field :password, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true %>
        </div>

        <div>
          <%= form.label :server, "Server", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.text_field :server, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true %>
        </div>

        <div>
          <%= form.label :volume_leverage, "Volume Leverage (Using when copying from host account, volume host account x volume_leverage = volume trade, default is 1.0)", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.number_field :volume_leverage, step: '0.5', min: '0.01', placeholder: '1.0', class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: false %>
        </div>
      <% else %>
        <div>
          <%= form.label :account_number, "Account Number", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.number_field :account_number, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true %>
          <p class="text-xs text-gray-400 mt-1">Enter your cTrader account number. The account ID will be fetched automatically.</p>
        </div>

        <div class="flex items-center">
          <%= form.check_box :auto_refresh_token, { checked: true, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" }, "true", "false" %>
          <%= form.label :auto_refresh_token, "Auto refresh token (automatically refresh token when it will expire within 48 hours)", class: "ml-2 block text-sm text-gray-300" %>
        </div>

        <%= form.hidden_field :account_id %>
        <%= form.hidden_field :host_type, value: 'demo' %>

        <div class="bg-gray-700 p-4 rounded-lg border border-gray-600 mt-4">
          <h3 class="text-sm font-medium text-gray-300 mb-2">Token Information</h3>
          <p class="text-xs text-gray-400 mb-4">
            After creating the account, click "Get Access Token" to authenticate with cTrader and automatically fetch your account details.
          </p>
        </div>
      <% end %>

      <div class="flex justify-between pt-4">
        <%= link_to "Cancel", accounts_path, class: "bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700" %>
        <%= form.submit "Create Account", class: "bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700" %>
      </div>
    <% end %>
  </div>
</div>

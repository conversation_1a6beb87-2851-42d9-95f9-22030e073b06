<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white">
        <% if @account.accountable_type == 'Mt5Account' %>
          Edit MT5 Account
        <% else %>
          Edit CTrader Account
        <% end %>
      </h1>
    </div>

    <%= form_with(model: @account, url: account_path(@account), method: :patch, class: "space-y-4") do |form| %>
      <% if @account.errors.any? %>
        <div class="bg-red-800 text-white p-4 rounded-lg mb-4">
          <h2 class="text-lg font-semibold mb-2">
            <%= pluralize(@account.errors.count, "error") %> prohibited this account from being saved:
          </h2>
          <ul class="list-disc list-inside">
            <% @account.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div>
        <%= form.label :name, "Account Name (optional)", class: "block text-sm font-medium text-gray-300 mb-1" %>
        <%= form.text_field :name, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", placeholder: "My Trading Account" %>
      </div>

      <% if @account.accountable_type == 'Mt5Account' %>
        <div>
          <%= form.label :login, "Login", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.number_field :login, value: @account.accountable.login, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true %>
        </div>

        <div>
          <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.password_field :password, value: @account.accountable.password, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true %>
        </div>

        <div>
          <%= form.label :server, "Server", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.text_field :server, value: @account.accountable.server, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true %>
        </div>

        <div>
          <%= form.label :volume_leverage, "Volume Leverage (Using when copying from host account, volume host account x volume_leverage = volume trade, default is 1.0)", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.number_field :volume_leverage, value: @account.accountable.volume_leverage, step: '0.01', min: '0.01', placeholder: '1.0', class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: false %>
        </div>
      <% else %>
        <div>
          <%= form.label :account_number, "Account Number", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <%= form.number_field :account_number, value: @account.accountable.account_number, class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true %>
        </div>

        <%= form.hidden_field :account_id, value: @account.accountable.account_id %>
        <%= form.hidden_field :host_type, value: @account.accountable.host_type %>

        <div class="bg-gray-700 p-4 rounded-lg border border-gray-600 mt-4 mb-4">
          <div class="flex justify-between items-center">
            <h3 class="text-sm font-medium text-gray-300">Account Details</h3>
          </div>
          <div class="grid grid-cols-2 gap-4 mt-2">
            <div>
              <p class="text-xs text-gray-400">Account ID:</p>
              <p class="text-sm text-white"><%= @account.accountable.account_id %></p>
            </div>
            <div>
              <p class="text-xs text-gray-400">Host Type:</p>
              <p class="text-sm text-white"><%= @account.accountable.host_type&.capitalize %></p>
            </div>
          </div>
        </div>

        <div class="bg-gray-700 p-4 rounded-lg border border-gray-600">
          <div class="flex justify-between items-center">
            <h3 class="text-sm font-medium text-gray-300">Token Information</h3>
            <div class="flex space-x-2">
              <%= link_to "Get New Token", get_ctrader_token_account_path(@account), class: "text-xs text-blue-400 hover:text-blue-300 bg-gray-600 px-2 py-1 rounded" %>
              <%= button_to "Refresh Token", refresh_ctrader_token_account_path(@account), method: :post, class: "text-xs text-yellow-400 hover:text-yellow-300 bg-gray-600 px-2 py-1 rounded border-none cursor-pointer", remote: true %>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4 mt-2">
            <div>
              <p class="text-xs text-gray-400">Token Status:</p>
              <p class="text-sm text-white">
                <% if @account.accountable.access_token.present? %>
                  <% if @account.accountable.token_expired? %>
                    <span class="px-2 py-1 text-xs rounded-full bg-yellow-800 text-yellow-200">Expired</span>
                  <% else %>
                    <span class="px-2 py-1 text-xs rounded-full bg-green-800 text-green-200">Active</span>
                  <% end %>
                <% else %>
                  <span class="px-2 py-1 text-xs rounded-full bg-red-800 text-red-200">Missing</span>
                <% end %>
              </p>
            </div>
            <div>
              <p class="text-xs text-gray-400">Expires:</p>
              <p class="text-sm text-white">
                <% if @account.accountable.access_token.present? && @account.accountable.updated_at.present? && @account.accountable.expires_in.present? %>
                  <%= (@account.accountable.updated_at + @account.accountable.expires_in.seconds).strftime("%B %d, %Y %H:%M") %>
                <% else %>
                  N/A
                <% end %>
              </p>
            </div>
          </div>
        </div>
      <% end %>

      <div class="flex justify-between pt-4">
        <%= link_to "Cancel", accounts_path, class: "bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700" %>
        <%= form.submit "Update Account", class: "bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700" %>
      </div>
    <% end %>
  </div>
</div>

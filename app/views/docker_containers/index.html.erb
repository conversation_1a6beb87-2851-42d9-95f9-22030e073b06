<div class="container mx-auto px-4 py-8" data-controller="modal">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Docker Containers</h1>
    <div class="flex space-x-4">
      <div class="relative group">
        <%= button_to "Initialize Base Services", init_base_services_docker_containers_path, method: :post, class: "bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded", data: { turbo: true } %>
        <div class="absolute z-10 hidden group-hover:block bg-gray-200 dark:bg-gray-800 text-gray-900 dark:text-white text-sm rounded p-2 w-64 mt-1 right-0">
          Starts containers defined in syncbot-docker-compose.yaml (mt5, rabbitmq, celery_worker, beater)
        </div>
      </div>

      <!-- Confirmation Modal -->
      <div id="destroy-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
          <div class="mt-3 text-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Confirm Deletion</h3>
            <div class="mt-2 px-7 py-3">
              <p class="text-sm text-gray-500 dark:text-gray-400">Are you sure you want to destroy all containers? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center mt-4 gap-4">
              <%= button_to "Yes, Destroy All", destroy_all_containers_docker_containers_path(confirm: true), method: :post, class: "bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" %>
              <button id="cancel-destroy" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-white font-bold py-2 px-4 rounded">Cancel</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="mb-6">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Clone Celery Worker</h2>
    <div class="bg-white dark:bg-gray-800 shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <%= form_with url: clone_celery_worker_docker_containers_path, method: :post do |f| %>
        <div class="flex flex-col md:flex-row md:items-end">
          <div class="mr-4 flex-grow mb-4 md:mb-0">
            <%= f.label :name_suffix, "Name Suffix", class: "block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" %>
            <%= f.text_field :name_suffix, class: "shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 leading-tight focus:outline-none focus:shadow-outline", placeholder: "e.g. client1" %>
          </div>

          <div class="mr-4 mb-4 md:mb-0">
            <div class="flex flex-col">
              <%= f.label :host_port, "Host Port for MT5 (3000)", class: "block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" %>
              <div class="flex items-center">
                <%= f.number_field :host_port, min: 1024, max: 65535, class: "shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 leading-tight focus:outline-none focus:shadow-outline", placeholder: "Leave blank for no binding" %>
                <div class="ml-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 cursor-pointer" title="Enter a port number to bind MT5's port 3000 to the host. Leave blank to only expose the port to other containers.">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <%= f.submit "Clone Containers", class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" %>
        </div>
      <% end %>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">This will create a new MT5 container and a new celery_worker container with the given suffix. If you specify a host port, MT5's port 3000 will be bound to that port on the host. Port 8001 will only be accessible from other containers.</p>
    </div>
  </div>

  <div class="mb-6">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Add Server to MT5</h2>
    <div class="bg-white dark:bg-gray-800 shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <%= form_with url: add_mt5_server_docker_containers_path, method: :post do |f| %>
        <div class="flex flex-col md:flex-row md:items-end">
          <div class="mr-4 flex-grow mb-4 md:mb-0">
            <%= f.label :server_name, "Server Name", class: "block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" %>
            <%= f.text_field :server_name, class: "shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 leading-tight focus:outline-none focus:shadow-outline", placeholder: "Enter server name" %>
          </div>

          <div class="mr-4 mb-4 md:mb-0">
            <div class="flex flex-col">
              <%= f.label :container_name, "MT5 Container", class: "block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" %>
              <div class="flex items-center">
                <% mt5_containers = @containers.select { |c| c.name.include?('mt5') && c.status == 'running' }.map { |c| [c.name, c.name] } %>
                <%= f.select :container_name, [["All MT5 Containers", "all"]] + mt5_containers, {}, class: "shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 leading-tight focus:outline-none focus:shadow-outline" %>
              </div>
            </div>
          </div>

          <%= f.submit "Add Server", class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" %>
        </div>
      <% end %>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">This will run the command to add a server to the selected MT5 container(s): <code class="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded">docker exec -u 911:911 &lt;mt5-container-name&gt; wine cmd /c "python /scripts/mt5serversearch.py &lt;server-name&gt;"</code></p>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow-md rounded my-6">
    <table class="min-w-full bg-white dark:bg-gray-800">
      <thead>
        <tr>
          <th class="py-3 px-4 bg-gray-100 dark:bg-gray-700 font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider text-left">Name</th>
          <th class="py-3 px-4 bg-gray-100 dark:bg-gray-700 font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider text-left">Image</th>
          <th class="py-3 px-4 bg-gray-100 dark:bg-gray-700 font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider text-left">Status</th>
          <th class="py-3 px-4 bg-gray-100 dark:bg-gray-700 font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider text-left">Network</th>
          <th class="py-3 px-4 bg-gray-100 dark:bg-gray-700 font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider text-left">Ports</th>
          <th class="py-3 px-4 bg-gray-100 dark:bg-gray-700 font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider text-left">Created</th>
          <th class="py-3 px-4 bg-gray-100 dark:bg-gray-700 font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider text-left">Actions</th>
        </tr>
      </thead>
      <tbody>
        <% if @containers.empty? %>
          <tr>
            <td colspan="7" class="py-8 px-4 text-center text-gray-500 dark:text-gray-400">
              <p class="mb-2">No Docker containers found.</p>
              <p>Click "Initialize Base Services" to start the base containers.</p>
            </td>
          </tr>
        <% else %>
          <% @containers.each do |container| %>
            <tr class="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="py-4 px-4 text-gray-900 dark:text-white"><%= container.name %></td>
              <td class="py-4 px-4 text-gray-900 dark:text-white"><%= container.image %></td>
              <td class="py-4 px-4">
                <% if container.status == 'running' %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    <%= container.status %>
                  </span>
                <% elsif container.status == 'exited' %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                    <%= container.status %>
                  </span>
                <% else %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                    <%= container.status %>
                  </span>
                <% end %>
              </td>
              <td class="py-4 px-4">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  <%= container.network %>
                </span>
              </td>
              <td class="py-4 px-4 text-sm text-gray-900 dark:text-white"><%= container.ports %></td>
              <td class="py-4 px-4 text-gray-900 dark:text-white"><%= container.created_at.strftime("%Y-%m-%d %H:%M:%S") %></td>
              <td class="py-4 px-4">
                <div class="flex space-x-2">
                  <% if container.status == 'running' %>
                    <%= button_to "Stop", stop_docker_container_path(id: container.id), method: :post, class: "bg-yellow-500 hover:bg-yellow-700 text-white text-xs py-1 px-2 rounded", data: { turbo: true } %>
                    <%= button_to "Restart", restart_docker_container_path(id: container.id), method: :post, class: "bg-blue-500 hover:bg-blue-700 text-white text-xs py-1 px-2 rounded", data: { turbo: true } %>
                  <% else %>
                    <%= button_to "Start", start_docker_container_path(id: container.id), method: :post, class: "bg-green-500 hover:bg-green-700 text-white text-xs py-1 px-2 rounded", data: { turbo: true } %>
                  <% end %>
                  <%= button_to "Delete", docker_container_path(id: container.id), method: :delete, class: "bg-red-500 hover:bg-red-700 text-white text-xs py-1 px-2 rounded", data: { confirm: "Are you sure you want to delete this container?", turbo: true } %>
                </div>
              </td>
            </tr>
          <% end %>
        <% end %>
      </tbody>
    </table>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Modal for destroy all containers
    const destroyBtn = document.getElementById('destroy-all-btn');
    const destroyModal = document.getElementById('destroy-modal');
    const cancelBtn = document.getElementById('cancel-destroy');

    if (destroyBtn && destroyModal && cancelBtn) {
      destroyBtn.addEventListener('click', function() {
        destroyModal.classList.remove('hidden');
      });

      cancelBtn.addEventListener('click', function() {
        destroyModal.classList.add('hidden');
      });
    }
  });
</script>
